import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../chat/chat.helpers.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";
import { UserModel } from "../../lib/db.js";
import { getToday } from "../../utils/dateHelpers.js";
import { logger } from "../../utils/log.js";
import gameConfig from "../../config/gameConfig.js";

const SHRINE_BUFFS = [
    {
        buffType: "rareDrops",
        description: "Increases rare item drop chance",
        primaryValue: 1.4,
        secondaryValue: 1.2,
    },
    {
        buffType: "craftSpeed",
        description: "Reduces crafting time of all items",
        primaryValue: 0.6,
        secondaryValue: 0.8,
    },
    {
        buffType: "damage",
        description: "Increases all damage done",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "exp",
        description: "Increases all EXP received",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "armour",
        description: "Increases armour",
        primaryValue: 1.25,
        secondaryValue: 1.1,
    },
    {
        buffType: "jail",
        description: "Reduces jail time",
        primaryValue: 0.5,
        secondaryValue: 0.25,
    },
    {
        buffType: "mission",
        description: "Reduces mission durations",
        primaryValue: 0.5,
        secondaryValue: 0.25,
    },
    {
        buffType: "yenEarnings",
        description: "Increases all Yen from encounters and job payouts",
        primaryValue: 1.5,
        secondaryValue: 1.25,
    },
    {
        buffType: "auctionFees",
        description: "Reduces auction listing fees",
        primaryValue: 0.5,
        secondaryValue: 0.75,
    },
    {
        buffType: "exploreSpawn",
        description: "Increases spawn rate of explore map nodes",
        primaryValue: 1.5,
        secondaryValue: 1.25,
    },
    {
        buffType: "gatheringAmount",
        description: "Increases amount gathered from gathering nodes",
        primaryValue: 1.4,
        secondaryValue: 1.2,
    },
];

export const hasUserDonatedMinimumForSecondaryBuffs = async (userId: number, date: Date) => {
    const userDonation = await ShrineRepository.findUserDonationForDate(userId, date);
    if (!userDonation) {
        return false;
    }
    // Require at least the minimum donation amount for secondary buffs
    return userDonation.amount >= gameConfig.SHRINE_MINIMUM_DONATION;
};

/**
 * Determines if a user has access to a specific buff based on their donation status
 * @param buffData The buff configuration from shrine goal
 * @param userDonationAmount The user's donation amount for the day
 * @returns The buff value if accessible, null otherwise
 */
export const getUserBuffAccess = (
    buffData: { value: number; isPrimary: boolean },
    userDonationAmount: number
): number | null => {
    // Primary buffs are available to all players
    if (buffData.isPrimary) {
        return buffData.value;
    }

    // Secondary buffs require minimum donation
    const hasMinimumDonation = userDonationAmount >= gameConfig.SHRINE_MINIMUM_DONATION;
    return hasMinimumDonation ? buffData.value : null;
};

/**
 * Filters buff rewards based on user's donation status
 * @param buffRewards All available buff rewards
 * @param userDonationAmount The user's donation amount for the day
 * @returns Filtered buff rewards the user has access to
 */
export const filterAccessibleBuffs = (
    buffRewards: Record<string, { buffType: string; description: string; value: number; isPrimary: boolean }>,
    userDonationAmount: number
): PrismaJson.ShrineBuffRewards => {
    const accessibleBuffs: PrismaJson.ShrineBuffRewards = {};

    for (const [buffType, buffData] of Object.entries(buffRewards)) {
        const buffValue = getUserBuffAccess(buffData, userDonationAmount);
        if (buffValue !== null) {
            accessibleBuffs[buffType] = buffData;
        }
    }

    return accessibleBuffs;
};

/**
 * Validates donation amount
 * @param amount The donation amount to validate
 * @returns Error message if invalid, null if valid
 */
export const validateDonationAmount = (amount: number): string | null => {
    if (!amount) {
        return "Amount is required and must be a number";
    }
    if (amount < 0 || amount < gameConfig.SHRINE_MINIMUM_DONATION) {
        return "Invalid amount";
    }
    return null;
};

/**
 * Validates user has sufficient cash for donation
 * @param userCash User's current cash
 * @param donationAmount Amount to donate
 * @returns Error message if insufficient, null if valid
 */
export const validateUserCash = (userCash: number, donationAmount: number): string | null => {
    if (userCash < donationAmount) {
        return "Not enough cash";
    }
    return null;
};

export const dailyBuffIsActive = async (type: string, userId: number) => {
    const today = getToday();

    const [dailyShrine, userDonation] = await Promise.all([
        ShrineRepository.findDailyShrineGoalWithReached(today),
        ShrineRepository.findUserDonationForDate(userId, today),
    ]);

    if (!dailyShrine?.buffRewards?.[type]) {
        return null;
    }

    const buffData = dailyShrine.buffRewards[type];
    const userDonationAmount = userDonation?.amount ?? 0;

    return getUserBuffAccess(buffData, userDonationAmount);
};

export const getDailyShrineGoal = async (date: Date) => {
    return await ShrineRepository.findDailyShrineGoal(date);
};

export const addToDailyShrineGoal = async (user: UserModel, amount: number) => {
    const today = getToday();
    const dailyShrineGoal = await getDailyShrineGoal(today);
    if (!dailyShrineGoal) {
        return false;
    }

    dailyShrineGoal.donationAmount += amount;

    // Update user's donation record
    await ShrineRepository.upsertUserDonation(user.id, today, amount);

    // Check if goal is reached and send announcement if needed
    if (!dailyShrineGoal.goalReached && dailyShrineGoal.donationAmount >= dailyShrineGoal.donationGoal) {
        dailyShrineGoal.goalReached = true;

        await ChatHelper.SendAnnouncementMessage(
            "shrineGoalReached",
            `The Shrine daily donation goal has been reached. Global buffs are now active!`
        );
    }

    return await ShrineRepository.updateShrineGoal(dailyShrineGoal.id, {
        donationAmount: dailyShrineGoal.donationAmount,
        goalReached: dailyShrineGoal.goalReached,
    });
};

export const getRandomDailyBuffs = () => {
    const primaryBuff = SHRINE_BUFFS[Math.floor(Math.random() * SHRINE_BUFFS.length)];

    // Filter out the primary buff type to ensure different buff types
    const availableSecondaryBuffs = SHRINE_BUFFS.filter((buff) => buff.buffType !== primaryBuff.buffType);
    const secondaryBuff = availableSecondaryBuffs[Math.floor(Math.random() * availableSecondaryBuffs.length)];

    const primary = {
        buffType: primaryBuff.buffType,
        description: primaryBuff.description,
        value: primaryBuff.primaryValue,
        isPrimary: true,
    };

    const secondary = {
        buffType: secondaryBuff.buffType,
        description: secondaryBuff.description,
        value: secondaryBuff.secondaryValue,
        isPrimary: false,
    };

    return { [primary.buffType]: primary, [secondary.buffType]: secondary };
};

export const announceDonationGoalReset = async (donationGoal: number | string) => {
    try {
        await ChatHelper.SendAnnouncementMessage("shrineGoalReset", JSON.stringify({ goal: donationGoal }));
    } catch (error) {
        logger.error(`Failed to send chat message: ${error}`);
    }
};
