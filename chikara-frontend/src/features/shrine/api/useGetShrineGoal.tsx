import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { type AppRouterClient } from "@/lib/orpc";

export type ShrineGoal = Awaited<ReturnType<AppRouterClient["shrine"]["getGoal"]>>;

export const useGetShrineGoal = (options: QueryOptions = {}) => {
    return useQuery(
        api.shrine.getGoal.queryOptions({
            staleTime: 60000, // 1 minute
            ...options,
        })
    );
};
