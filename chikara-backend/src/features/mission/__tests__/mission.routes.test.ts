import * as MissionController from "../mission.controller.js";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock dependencies
vi.mock("@/features/mission/mission.controller.js");
vi.mock("@/middleware/authMiddleware.js");
vi.mock("@/utils/routeHandler.js");

const mockMissionController = vi.mocked(MissionController);

describe("Mission Routes", () => {
    const mockUser = {
        id: 1,
        username: "testuser",
        currentMission: null,
    };

    const mockUserOnMission = {
        id: 1,
        username: "testuser",
        currentMission: 5,
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("Mission List Route Handler", () => {
        it("should call missionList controller", async () => {
            const mockMissions = [
                {
                    id: 1,
                    missionName: "Test Mission 1",
                    duration: 7200000,
                    levelReq: 5,
                    rewardType: "cash",
                },
                {
                    id: 2,
                    missionName: "Test Mission 2",
                    duration: 14400000,
                    levelReq: 10,
                    rewardType: "exp",
                },
            ];

            mockMissionController.missionList.mockResolvedValue({ data: mockMissions });

            const result = await MissionController.missionList(1);

            expect(result).toEqual({ data: mockMissions });
            expect(mockMissionController.missionList).toHaveBeenCalledTimes(1);
        });

        it("should handle mission list errors", async () => {
            mockMissionController.missionList.mockResolvedValue({
                error: "Failed to fetch missions",
                statusCode: 500,
            });

            const result = await MissionController.missionList(1);

            expect(result).toEqual({
                error: "Failed to fetch missions",
                statusCode: 500,
            });
        });
    });

    describe("Current Mission Route Handler", () => {
        it("should call currentMission controller with mission ID", async () => {
            const mockMission = {
                id: 5,
                missionName: "Current Mission",
                duration: 7200000,
                levelReq: 5,
            };

            mockMissionController.currentMission.mockResolvedValue({ data: mockMission });

            const result = await MissionController.currentMission(mockUserOnMission.currentMission);

            expect(result).toEqual({ data: mockMission });
            expect(mockMissionController.currentMission).toHaveBeenCalledWith(5);
        });

        it("should handle no current mission error", async () => {
            mockMissionController.currentMission.mockResolvedValue({
                error: "Not on a mission!",
                statusCode: 400,
            });

            const result = await MissionController.currentMission(null);

            expect(result).toEqual({
                error: "Not on a mission!",
                statusCode: 400,
            });
        });
    });

    describe("Start Mission Route Handler", () => {
        it("should call startMission controller with user ID and mission ID", async () => {
            const missionId = 1;
            const userId = mockUser.id;

            mockMissionController.startMission.mockResolvedValue({
                data: "Mission started successfully",
            });

            const result = await MissionController.startMission(userId, missionId);

            expect(result).toEqual({
                data: "Mission started successfully",
            });
            expect(mockMissionController.startMission).toHaveBeenCalledWith(userId, missionId);
        });

        it("should handle mission start validation errors", async () => {
            mockMissionController.startMission.mockResolvedValue({
                error: "Mission ID is required",
                statusCode: 400,
            });

            const result = await MissionController.startMission(1, null);

            expect(result).toEqual({
                error: "Mission ID is required",
                statusCode: 400,
            });
        });

        it("should handle mission not found error", async () => {
            const missionId = 999;
            const userId = mockUser.id;

            mockMissionController.startMission.mockResolvedValue({
                error: "Mission not found",
                statusCode: 404,
            });

            const result = await MissionController.startMission(userId, missionId);

            expect(result).toEqual({
                error: "Mission not found",
                statusCode: 404,
            });
        });

        it("should handle user already on mission error", async () => {
            const missionId = 1;
            const userId = mockUser.id;

            mockMissionController.startMission.mockResolvedValue({
                error: "Already on a mission!",
                statusCode: 400,
            });

            const result = await MissionController.startMission(userId, missionId);

            expect(result).toEqual({
                error: "Already on a mission!",
                statusCode: 400,
            });
        });

        it("should handle user eligibility errors", async () => {
            const missionId = 1;
            const userId = mockUser.id;

            mockMissionController.startMission.mockResolvedValue({
                error: "Not Eligible",
                statusCode: 400,
            });

            const result = await MissionController.startMission(userId, missionId);

            expect(result).toEqual({
                error: "Not Eligible",
                statusCode: 400,
            });
        });

        it("should handle incapacitated user errors", async () => {
            const missionId = 1;
            const userId = mockUser.id;

            mockMissionController.startMission.mockResolvedValue({
                error: "Can't start a mission while incapacitated",
                statusCode: 400,
            });

            const result = await MissionController.startMission(userId, missionId);

            expect(result).toEqual({
                error: "Can't start a mission while incapacitated",
                statusCode: 400,
            });
        });

        it("should handle internal server errors", async () => {
            const missionId = 1;
            const userId = mockUser.id;

            mockMissionController.startMission.mockResolvedValue({
                error: "Failed to start mission due to an internal error",
                statusCode: 500,
            });

            const result = await MissionController.startMission(userId, missionId);

            expect(result).toEqual({
                error: "Failed to start mission due to an internal error",
                statusCode: 500,
            });
        });
    });

    describe("Cancel Mission Route Handler", () => {
        it("should call cancelMission controller with user ID", async () => {
            const userId = mockUser.id;

            mockMissionController.cancelMission.mockResolvedValue({
                data: "Mission cancelled successfully",
            });

            const result = await MissionController.cancelMission(userId);

            expect(result).toEqual({
                data: "Mission cancelled successfully",
            });
            expect(mockMissionController.cancelMission).toHaveBeenCalledWith(userId);
        });

        it("should handle user not found error", async () => {
            mockMissionController.cancelMission.mockResolvedValue({
                error: "User not found",
                statusCode: 404,
            });

            const result = await MissionController.cancelMission(1);

            expect(result).toEqual({
                error: "User not found",
                statusCode: 404,
            });
        });

        it("should handle not on mission error", async () => {
            mockMissionController.cancelMission.mockResolvedValue({
                error: "Not on a mission!",
                statusCode: 400,
            });

            const result = await MissionController.cancelMission(1);

            expect(result).toEqual({
                error: "Not on a mission!",
                statusCode: 400,
            });
        });

        it("should handle banned user error", async () => {
            mockMissionController.cancelMission.mockResolvedValue({
                error: "Banned from mission cancelling",
                statusCode: 400,
            });

            const result = await MissionController.cancelMission(14);

            expect(result).toEqual({
                error: "Banned from mission cancelling",
                statusCode: 400,
            });
        });

        it("should handle internal server errors", async () => {
            mockMissionController.cancelMission.mockResolvedValue({
                error: "Failed to cancel mission due to an internal error",
                statusCode: 500,
            });

            const result = await MissionController.cancelMission(1);

            expect(result).toEqual({
                error: "Failed to cancel mission due to an internal error",
                statusCode: 500,
            });
        });
    });

    describe("Route Integration Tests", () => {
        it("should handle different user scenarios", async () => {
            // Test with user not on mission
            mockMissionController.currentMission.mockResolvedValue({
                error: "Not on a mission!",
                statusCode: 400,
            });

            let result = await MissionController.currentMission(null);
            expect(result.error).toBe("Not on a mission!");

            // Test with user on mission
            mockMissionController.currentMission.mockResolvedValue({
                data: { id: 1, missionName: "Test Mission" },
            });

            result = await MissionController.currentMission(1);
            expect(result.data).toEqual({ id: 1, missionName: "Test Mission" });
        });

        it("should handle mission ID validation", async () => {
            // Test invalid mission ID
            mockMissionController.startMission.mockResolvedValue({
                error: "Mission ID is required",
                statusCode: 400,
            });

            let result = await MissionController.startMission(1, null);
            expect(result.error).toBe("Mission ID is required");

            // Test valid mission ID
            mockMissionController.startMission.mockResolvedValue({
                data: "Mission started successfully",
            });

            result = await MissionController.startMission(1, 1);
            expect(result.data).toBe("Mission started successfully");
        });
    });
});
