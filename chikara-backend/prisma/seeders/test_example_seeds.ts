import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

/**
 * Comprehensive test seed file for all quest objective types
 * This file creates quests that cover every quest objective type for testing purposes
 * All quests have level requirement of 1 for easy testing
 */

// Supporting data for quest objectives
const testItems = [
    { id: 9001, name: "Test Copper Ore", itemType: "crafting", rarity: "novice", level: 1, cashValue: 10 },
    { id: 9002, name: "Test Iron Ore", itemType: "crafting", rarity: "standard", level: 1, cashValue: 20 },
    { id: 9003, name: "Test Magic Crystal", itemType: "crafting", rarity: "enhanced", level: 1, cashValue: 50 },
    { id: 9004, name: "Test Bread", itemType: "consumable", rarity: "novice", level: 1, cashValue: 5 },
    { id: 9005, name: "Test Steel Sword", itemType: "weapon", rarity: "standard", level: 1, damage: 10, cashValue: 100 },
    { id: 9006, name: "Test Health Potion", itemType: "consumable", rarity: "standard", level: 1, health: 50, cashValue: 25 },
    { id: 9007, name: "Test Quest Document", itemType: "quest", rarity: "novice", level: 1, cashValue: 0 },
    { id: 9008, name: "Test Ring", itemType: "finger", rarity: "enhanced", level: 1, cashValue: 200 },
    { id: 9009, name: "Test Herbs", itemType: "crafting", rarity: "novice", level: 1, cashValue: 15 },
    { id: 9010, name: "Test Special Device", itemType: "special", rarity: "specialist", level: 1, cashValue: 500 },
];

const testCreatures = [
    { id: 9001, name: "Test Training Dummy", image: "training_dummy.png", minFloor: 1, maxFloor: 5, boss: false, health: 100, strength: 10, defence: 5, weaponDamage: 15, location: "school", statType: "balanced" },
    { id: 9002, name: "Test Street Thug", image: "street_thug.png", minFloor: 1, maxFloor: 10, boss: false, health: 150, strength: 15, defence: 8, weaponDamage: 20, location: "alley", statType: "dps" },
    { id: 9003, name: "Test Guardian", image: "guardian.png", minFloor: 10, maxFloor: 20, boss: true, health: 500, strength: 25, defence: 20, weaponDamage: 35, location: "shrine", statType: "tank" },
    { id: 9004, name: "Test Sewer Monster", image: "sewer_monster.png", minFloor: 5, maxFloor: 15, boss: false, health: 200, strength: 18, defence: 10, weaponDamage: 25, location: "sewers", statType: "balanced" },
    { id: 9005, name: "Test Boss", image: "test_boss.png", minFloor: 15, maxFloor: 25, boss: true, health: 800, strength: 30, defence: 25, weaponDamage: 45, location: "mall", statType: "tank" },
];

// Test quests covering all objective types
const testQuests = [
    {
        id: 9001,
        name: "Test: Defeat Specific NPC",
        description: "Test quest for DEFEAT_NPC objective with specific creature",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9002,
        name: "Test: Defeat Any NPC",
        description: "Test quest for DEFEAT_NPC objective with any creature",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9003,
        name: "Test: Defeat NPC in Turns",
        description: "Test quest for DEFEAT_NPC_IN_TURNS objective",
        levelReq: 1,
        cashReward: 150,
        xpReward: 15,
        repReward: 0.15,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9004,
        name: "Test: Defeat NPC with Low Damage",
        description: "Test quest for DEFEAT_NPC_WITH_LOW_DAMAGE objective",
        levelReq: 1,
        cashReward: 200,
        xpReward: 20,
        repReward: 0.2,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9005,
        name: "Test: Defeat Boss",
        description: "Test quest for DEFEAT_BOSS objective",
        levelReq: 1,
        cashReward: 300,
        xpReward: 30,
        repReward: 0.3,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9006,
        name: "Test: Defeat Player",
        description: "Test quest for DEFEAT_PLAYER objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test PvP Objectives",
    },
    {
        id: 9007,
        name: "Test: PvP Post Battle Choice",
        description: "Test quest for PVP_POST_BATTLE_CHOICE objective",
        levelReq: 1,
        cashReward: 150,
        xpReward: 15,
        repReward: 0.15,
        questChainName: "Test PvP Objectives",
    },
    {
        id: 9008,
        name: "Test: Defeat Player with Name",
        description: "Test quest for DEFEAT_PLAYER_XNAME objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test PvP Objectives",
    },
    {
        id: 9009,
        name: "Test: Defeat Specific Player",
        description: "Test quest for DEFEAT_SPECIFIC_PLAYER objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test PvP Objectives",
    },
    {
        id: 9010,
        name: "Test: Win Battle",
        description: "Test quest for WIN_BATTLE objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9011,
        name: "Test: Use Ability",
        description: "Test quest for USE_ABILITY objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Battle Objectives",
    },
    {
        id: 9012,
        name: "Test: Acquire Specific Item",
        description: "Test quest for ACQUIRE_ITEM objective with specific item",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Item Objectives",
    },
    {
        id: 9013,
        name: "Test: Craft Specific Item",
        description: "Test quest for CRAFT_ITEM objective with specific item",
        levelReq: 1,
        cashReward: 150,
        xpReward: 15,
        repReward: 0.15,
        questChainName: "Test Item Objectives",
    },
    {
        id: 9014,
        name: "Test: Craft Any Item",
        description: "Test quest for CRAFT_ITEM objective with any item",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Item Objectives",
    },
    {
        id: 9015,
        name: "Test: Deliver Item",
        description: "Test quest for DELIVER_ITEM objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Item Objectives",
    },
    {
        id: 9016,
        name: "Test: Gather Resources (Mining)",
        description: "Test quest for GATHER_RESOURCES objective with mining",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Gathering Objectives",
    },
    {
        id: 9017,
        name: "Test: Gather Resources (Scavenging)",
        description: "Test quest for GATHER_RESOURCES objective with scavenging",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Gathering Objectives",
    },
    {
        id: 9018,
        name: "Test: Gather Resources (Foraging)",
        description: "Test quest for GATHER_RESOURCES objective with foraging",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Gathering Objectives",
    },
    {
        id: 9019,
        name: "Test: Place Bounty",
        description: "Test quest for PLACE_BOUNTY objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Bounty Objectives",
    },
    {
        id: 9020,
        name: "Test: Collect Bounty Reward",
        description: "Test quest for COLLECT_BOUNTY_REWARD objective",
        levelReq: 1,
        cashReward: 150,
        xpReward: 15,
        repReward: 0.15,
        questChainName: "Test Bounty Objectives",
    },
    {
        id: 9021,
        name: "Test: Complete Missions",
        description: "Test quest for COMPLETE_MISSIONS objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Misc Objectives",
    },
    {
        id: 9022,
        name: "Test: Donate to Shrine",
        description: "Test quest for DONATE_TO_SHRINE objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Misc Objectives",
    },
    {
        id: 9023,
        name: "Test: Vote on Suggestion",
        description: "Test quest for VOTE_ON_SUGGESTION objective",
        levelReq: 1,
        cashReward: 50,
        xpReward: 5,
        repReward: 0.05,
        questChainName: "Test Misc Objectives",
    },
    {
        id: 9024,
        name: "Test: Character Encounters",
        description: "Test quest for CHARACTER_ENCOUNTERS objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Misc Objectives",
    },
    {
        id: 9025,
        name: "Test: Train Stats",
        description: "Test quest for TRAIN_STATS objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Misc Objectives",
    },
    {
        id: 9026,
        name: "Test: Gambling Slots",
        description: "Test quest for GAMBLING_SLOTS objective",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Misc Objectives",
    },
    {
        id: 9027,
        name: "Test: Complete Story Episode",
        description: "Test quest for COMPLETE_STORY_EPISODE objective",
        levelReq: 1,
        cashReward: 200,
        xpReward: 20,
        repReward: 0.2,
        questChainName: "Test Story Objectives",
    },
    {
        id: 9028,
        name: "Test: Unique Objective",
        description: "Test quest for UNIQUE_OBJECTIVE type",
        levelReq: 1,
        cashReward: 100,
        xpReward: 10,
        repReward: 0.1,
        questChainName: "Test Special Objectives",
    },
];

// Quest objectives covering all types
const testQuestObjectives = [
    // DEFEAT_NPC - specific creature
    {
        questId: 9001,
        objectiveType: "DEFEAT_NPC",
        target: 9001,
        targetAction: null,
        quantity: 3,
        location: "school",
        description: "Defeat 3 Test Training Dummies",
        creatureId: 9001,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_NPC - any creature in location
    {
        questId: 9002,
        objectiveType: "DEFEAT_NPC",
        target: null,
        targetAction: null,
        quantity: 5,
        location: "alley",
        description: "Defeat any 5 creatures in the alley",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_NPC_IN_TURNS
    {
        questId: 9003,
        objectiveType: "DEFEAT_NPC_IN_TURNS",
        target: 3,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "Defeat 2 enemies within 3 turns each",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_NPC_WITH_LOW_DAMAGE
    {
        questId: 9004,
        objectiveType: "DEFEAT_NPC_WITH_LOW_DAMAGE",
        target: 20,
        targetAction: null,
        quantity: 3,
        location: "school",
        description: "Defeat 3 enemies while taking less than 20% damage",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_BOSS
    {
        questId: 9005,
        objectiveType: "DEFEAT_BOSS",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "Defeat 1 boss creature",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_PLAYER
    {
        questId: 9006,
        objectiveType: "DEFEAT_PLAYER",
        target: 10,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "Defeat 2 players under level 10",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // PVP_POST_BATTLE_CHOICE
    {
        questId: 9007,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: null,
        targetAction: "mug",
        quantity: 3,
        location: null,
        description: "Win 3 PvP battles and choose to mug opponents",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_PLAYER_XNAME
    {
        questId: 9008,
        objectiveType: "DEFEAT_PLAYER_XNAME",
        target: null,
        targetAction: "test",
        quantity: 1,
        location: null,
        description: "Defeat 1 player with 'test' in their name",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DEFEAT_SPECIFIC_PLAYER
    {
        questId: 9009,
        objectiveType: "DEFEAT_SPECIFIC_PLAYER",
        target: 1,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "Defeat specific player with ID 1",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // WIN_BATTLE
    {
        questId: 9010,
        objectiveType: "WIN_BATTLE",
        target: null,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "Win 5 battles of any type",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // USE_ABILITY - specific ability
    {
        questId: 9011,
        objectiveType: "USE_ABILITY",
        target: 1,
        targetAction: null,
        quantity: 10,
        location: null,
        description: "Use ability ID 1 ten times",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // USE_ABILITY - any ability
    {
        questId: 9011,
        objectiveType: "USE_ABILITY",
        target: null,
        targetAction: null,
        quantity: 15,
        location: null,
        description: "Use any ability 15 times",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    
    // ACQUIRE_ITEM - specific item
    {
        questId: 9012,
        objectiveType: "ACQUIRE_ITEM",
        target: 9001,
        targetAction: null,
        quantity: 5,
        location: "school",
        description: "Acquire 5 Test Copper Ore",
        creatureId: null,
        itemId: 9001,
        isRequired: true,
    },
    
    // CRAFT_ITEM - specific item
    {
        questId: 9013,
        objectiveType: "CRAFT_ITEM",
        target: 9004,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "Craft 3 Test Bread",
        creatureId: null,
        itemId: 9004,
        isRequired: true,
    },
    
    // CRAFT_ITEM - any item
    {
        questId: 9014,
        objectiveType: "CRAFT_ITEM",
        target: null,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "Craft any 5 items",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DELIVER_ITEM
    {
        questId: 9015,
        objectiveType: "DELIVER_ITEM",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "Deliver 3 Test Quest Documents",
        creatureId: null,
        itemId: 9007,
        isRequired: true,
    },
    
    // GATHER_RESOURCES - mining specific item
    {
        questId: 9016,
        objectiveType: "GATHER_RESOURCES",
        target: 9002,
        targetAction: "mining",
        quantity: 4,
        location: null,
        description: "Mine 4 Test Iron Ore",
        creatureId: null,
        itemId: 9002,
        isRequired: true,
    },
    
    // GATHER_RESOURCES - mining any item
    {
        questId: 9016,
        objectiveType: "GATHER_RESOURCES",
        target: null,
        targetAction: "mining",
        quantity: 10,
        location: null,
        description: "Mine any 10 items",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    
    // GATHER_RESOURCES - scavenging specific item
    {
        questId: 9017,
        objectiveType: "GATHER_RESOURCES",
        target: 9010,
        targetAction: "scavenging",
        quantity: 2,
        location: null,
        description: "Scavenge 2 Test Special Devices",
        creatureId: null,
        itemId: 9010,
        isRequired: true,
    },
    
    // GATHER_RESOURCES - scavenging any item
    {
        questId: 9017,
        objectiveType: "GATHER_RESOURCES",
        target: null,
        targetAction: "scavenging",
        quantity: 8,
        location: null,
        description: "Scavenge any 8 items",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    
    // GATHER_RESOURCES - foraging specific item
    {
        questId: 9018,
        objectiveType: "GATHER_RESOURCES",
        target: 9009,
        targetAction: "foraging",
        quantity: 6,
        location: null,
        description: "Forage 6 Test Herbs",
        creatureId: null,
        itemId: 9009,
        isRequired: true,
    },
    
    // GATHER_RESOURCES - foraging any item
    {
        questId: 9018,
        objectiveType: "GATHER_RESOURCES",
        target: null,
        targetAction: "foraging",
        quantity: 12,
        location: null,
        description: "Forage any 12 items",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    
    // PLACE_BOUNTY
    {
        questId: 9019,
        objectiveType: "PLACE_BOUNTY",
        target: 100,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "Place a bounty of at least 100 yen",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // COLLECT_BOUNTY_REWARD
    {
        questId: 9020,
        objectiveType: "COLLECT_BOUNTY_REWARD",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "Collect 1 bounty reward",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // COMPLETE_MISSIONS
    {
        questId: 9021,
        objectiveType: "COMPLETE_MISSIONS",
        target: null,
        targetAction: null,
        quantity: 2,
        location: null,
        description: "Complete 2 missions",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // DONATE_TO_SHRINE
    {
        questId: 9022,
        objectiveType: "DONATE_TO_SHRINE",
        target: null,
        targetAction: null,
        quantity: 500,
        location: null,
        description: "Donate 500 yen to the shrine",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // VOTE_ON_SUGGESTION
    {
        questId: 9023,
        objectiveType: "VOTE_ON_SUGGESTION",
        target: null,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "Vote on 3 community suggestions",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // CHARACTER_ENCOUNTERS
    {
        questId: 9024,
        objectiveType: "CHARACTER_ENCOUNTERS",
        target: null,
        targetAction: null,
        quantity: 2,
        location: "school",
        description: "Complete 2 character encounters at school",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // TRAIN_STATS
    {
        questId: 9025,
        objectiveType: "TRAIN_STATS",
        target: null,
        targetAction: null,
        quantity: 5,
        location: null,
        description: "Train stats 5 times",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // GAMBLING_SLOTS
    {
        questId: 9026,
        objectiveType: "GAMBLING_SLOTS",
        target: null,
        targetAction: null,
        quantity: 1000,
        location: null,
        description: "Gamble 1000 yen on slots",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // COMPLETE_STORY_EPISODE
    {
        questId: 9027,
        objectiveType: "COMPLETE_STORY_EPISODE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: "shibuya",
        description: "Complete a story episode in Shibuya",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
    
    // UNIQUE_OBJECTIVE
    {
        questId: 9028,
        objectiveType: "UNIQUE_OBJECTIVE",
        target: null,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "Complete this unique test objective",
        creatureId: null,
        itemId: null,
        isRequired: true,
    },
];

// Additional objectives to test edge cases
const additionalObjectives = [
    // PVP_POST_BATTLE_CHOICE with different actions
    {
        questId: 9007,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: null,
        targetAction: "cripple",
        quantity: 2,
        location: null,
        description: "Win 2 PvP battles and choose to cripple opponents",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    {
        questId: 9007,
        objectiveType: "PVP_POST_BATTLE_CHOICE",
        target: null,
        targetAction: "leave",
        quantity: 1,
        location: null,
        description: "Win 1 PvP battle and choose to leave opponent alone",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    
    // DEFEAT_PLAYER with level ranges
    {
        questId: 9006,
        objectiveType: "DEFEAT_PLAYER",
        target: 5,
        targetAction: null,
        quantity: 1,
        location: null,
        description: "Defeat 1 player under level 5",
        creatureId: null,
        itemId: null,
        isRequired: false,
    },
    
    // GATHER_RESOURCES without specific activity type
    {
        questId: 9016,
        objectiveType: "GATHER_RESOURCES",
        target: 9001,
        targetAction: null,
        quantity: 3,
        location: null,
        description: "Gather 3 Test Copper Ore through any method",
        creatureId: null,
        itemId: 9001,
        isRequired: false,
    },
];

const insertData = async <T>(model: any, data: any[]): Promise<void> => {
    if (data.length > 0) {
        try {
            await model.createMany({
                data,
                skipDuplicates: true,
            });
        } catch (error) {
            console.error(`Error inserting data into ${model.name}:`, error);
            throw error;
        }
    }
};

async function main() {
    console.log("🌱 Seeding test quest objectives...");

    try {
        // Insert supporting data
        console.log("📦 Inserting test items...");
        await insertData(prisma.item, testItems);

        console.log("👹 Inserting test creatures...");
        await insertData(prisma.creature, testCreatures);

        console.log("📋 Inserting test quests...");
        await insertData(prisma.quest, testQuests);

        console.log("🎯 Inserting test quest objectives...");
        await insertData(prisma.quest_objective, testQuestObjectives);

        console.log("🎯 Inserting additional test quest objectives...");
        await insertData(prisma.quest_objective, additionalObjectives);

        console.log("✅ Test quest objectives seed completed successfully!");
        console.log(`
📊 Summary:
- Test Items: ${testItems.length}
- Test Creatures: ${testCreatures.length}
- Test Quests: ${testQuests.length}
- Test Quest Objectives: ${testQuestObjectives.length + additionalObjectives.length}

🎯 All Quest Objective Types Covered:
✓ DEFEAT_NPC (specific & any)
✓ DEFEAT_NPC_IN_TURNS
✓ DEFEAT_NPC_WITH_LOW_DAMAGE
✓ DEFEAT_BOSS
✓ DEFEAT_PLAYER
✓ PVP_POST_BATTLE_CHOICE (mug, cripple, leave)
✓ DEFEAT_PLAYER_XNAME
✓ DEFEAT_SPECIFIC_PLAYER
✓ WIN_BATTLE
✓ USE_ABILITY (specific & any)
✓ ACQUIRE_ITEM
✓ CRAFT_ITEM (specific & any)
✓ DELIVER_ITEM
✓ GATHER_RESOURCES (mining, scavenging, foraging)
✓ PLACE_BOUNTY
✓ COLLECT_BOUNTY_REWARD
✓ COMPLETE_MISSIONS
✓ DONATE_TO_SHRINE
✓ VOTE_ON_SUGGESTION
✓ CHARACTER_ENCOUNTERS
✓ TRAIN_STATS
✓ GAMBLING_SLOTS
✓ COMPLETE_STORY_EPISODE
✓ UNIQUE_OBJECTIVE

All quests have level requirement of 1 for easy testing.
        `);

    } catch (error) {
        console.error("❌ Error seeding test quest objectives:", error);
        throw error;
    }
}

main()
    .catch((e) => {
        console.error(e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });