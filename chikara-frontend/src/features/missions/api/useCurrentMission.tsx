import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { type AppRouterClient } from "@/lib/orpc";

export type CurrentMission = Awaited<ReturnType<AppRouterClient["mission"]["getCurrent"]>>;

export const useCurrentMission = (enabled = true, options = {}) => {
    return useQuery(
        api.missions.getCurrent.queryOptions({
            enabled,
            staleTime: 30000, // 30 seconds
            ...options,
        })
    );
};
