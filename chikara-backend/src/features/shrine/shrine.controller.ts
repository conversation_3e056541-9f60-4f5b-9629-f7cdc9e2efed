import gameConfig from "../../config/gameConfig.js";
import * as UserService from "../../core/user.service.js";
import * as ShrineHelper from "./shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { getToday } from "../../utils/dateHelpers.js";
import { emitShrineDonationMade } from "../../core/events/index.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";

export const isTodaysDonationGoalReached = async () => {
    const today = getToday();
    return await ShrineRepository.findDailyShrineGoalWithReached(today);
};

export const getActiveBuffsForUser = async (userId: number) => {
    const today = getToday();
    const [dailyShrine, userDonation] = await Promise.all([
        ShrineRepository.findDailyShrineGoalWithReached(today),
        ShrineRepository.findUserDonationForDate(userId, today),
    ]);

    if (!dailyShrine?.buffRewards) {
        return null;
    }

    const userDonationAmount = userDonation?.amount ?? 0;
    const buffRewards = dailyShrine.buffRewards as Record<
        string,
        { buffType: string; description: string; value: number; isPrimary: boolean }
    >;

    const activeBuffs = ShrineHelper.filterAccessibleBuffs(buffRewards, userDonationAmount);

    return {
        ...dailyShrine,
        buffRewards: activeBuffs,
    };
};

export const getUserDonationStatus = async (userId: number) => {
    const today = getToday();
    const userDonation = await ShrineRepository.findUserDonationForDate(userId, today);
    const donationAmount = userDonation?.amount || 0;
    const hasMinimumDonation = donationAmount >= gameConfig.SHRINE_MINIMUM_DONATION;

    return {
        hasMinimumDonation,
        donationAmount,
        minimumRequired: gameConfig.SHRINE_MINIMUM_DONATION,
    };
};

export const donateToShrine = async (userId: number, amount: number) => {
    // Validate donation amount
    const amountError = ShrineHelper.validateDonationAmount(amount);
    if (amountError) {
        return { error: amountError, statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }

    // Validate user has sufficient cash
    const cashError = ShrineHelper.validateUserCash(currentUser.cash, amount);
    if (cashError) {
        return { error: cashError, statusCode: 400 };
    }

    const dailyShrineGoal = await ShrineHelper.getDailyShrineGoal(getToday());
    if (!dailyShrineGoal) {
        return { error: "No daily shrine goal set for today", statusCode: 400 };
    }

    const updatedCash = currentUser.cash - amount;
    await UserService.updateUser(currentUser.id, { cash: updatedCash });

    const updatedShrineGoal = await ShrineHelper.addToDailyShrineGoal(currentUser, amount);
    if (!updatedShrineGoal) {
        return { error: "Failed to update shrine goal", statusCode: 500 };
    }

    await emitShrineDonationMade({
        userId: currentUser.id,
        amount,
    });

    logAction({
        action: "SHRINE_DONATION",
        userId: currentUser.id,
        info: {
            amount,
            totalDonations: updatedShrineGoal.donationAmount,
        },
    });

    return { data: `Donated ${amount} to daily shrine goal` };
};
