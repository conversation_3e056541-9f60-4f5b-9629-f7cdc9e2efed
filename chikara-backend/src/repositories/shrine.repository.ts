import { db } from "../lib/db.js";

export const findDailyShrineGoal = async (today: Date, onlyReached = false) => {
    const where: any = { goalDate: today };
    if (onlyReached) {
        where.goalReached = true;
    }

    return await db.shrine_goal.findFirst({
        where,
        orderBy: { id: "desc" },
    });
};

export const findDailyShrineGoalWithReached = async (today: Date) => {
    return await findDailyShrineGoal(today, true);
};

export const getDailyDonations = async (today: Date) => {
    return await db.shrine_donation.findMany({
        where: { date: today },
        orderBy: { updatedAt: "desc" },
        include: {
            user: {
                select: {
                    username: true,
                    avatar: true,
                },
            },
        },
    });
};

export const findUserDonationForDate = async (userId: number, date: Date) => {
    return await db.shrine_donation.findFirst({
        where: {
            userId,
            date,
        },
    });
};

export const createDonation = async (userId: number, date: Date, amount: number) => {
    return await db.shrine_donation.create({
        data: {
            userId,
            date,
            amount,
        },
    });
};

export const updateDonation = async (id: number, amount: number) => {
    return await db.shrine_donation.update({
        where: { id },
        data: { amount },
    });
};

/**
 * Creates or updates a user's donation for a specific date
 * @param userId User ID
 * @param date Donation date
 * @param amount Amount to add to existing donation or set as new donation
 * @returns Updated or created donation record
 */
export const upsertUserDonation = async (userId: number, date: Date, amount: number) => {
    const existingDonation = await findUserDonationForDate(userId, date);

    if (existingDonation) {
        return await updateDonation(existingDonation.id, existingDonation.amount + amount);
    } else {
        return await createDonation(userId, date, amount);
    }
};

export const updateShrineGoal = async (id: number, data: { donationAmount: number; goalReached: boolean }) => {
    return await db.shrine_goal.update({
        where: { id },
        data,
    });
};
