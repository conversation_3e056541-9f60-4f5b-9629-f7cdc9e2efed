import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import getScavengeLocation, { type InjuryType, type ScavengeLocation } from "../../data/scavengeLocations.js";
import {
    APOLLO_DIALOGUE,
    BAD_DIALOGUE_LINES,
    CHARACTER_NAMES,
    ENCOUNTER_LOCATIONS_IMAGES,
    ENCOUNTER_TYPES,
    GOOD_DIALOGUE_LINES,
    MAX_DOUBLE_EDGED_NODES,
    MAX_NODES,
    MIN_NODES,
    DOUBLE_EDGE_CHANCE,
    BATTLE_WEIGHT,
    BUFF_WEIGHT,
    CHARACTER_WEIGHT,
    SCAVENGE_NODE_WEIGHT,
    DEFAULT_ENCOUNTER_JAIL_DURATION_MS,
} from "./roguelike.constants.js";
import {
    findApplicableQuestsForDrops,
    findDropChanceByItemId,
    findPotentialDropsForRoguelike,
    findScavengeDrops,
} from "../../repositories/roguelike.repository.js";
import type { EncounterType, MapBuffs, MapNodeType, MapType } from "./roguelike.types.js";
import * as ShrineHelper from "../shrine/shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import type { StatusEffectModel, UserModel } from "../../lib/db.js";
import { NotificationTypes } from "../../types/notification.js";
import { DropChanceTypes, type LocationTypes } from "@prisma/client";
import { emitItemDropped } from "../../core/events/index.js";

function randomIntFromInterval(min: number, max: number) {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

function GetWeightedRandomNodeType(hasScavengeNodes: boolean) {
    const items: { item: EncounterType; weight: number }[] = [
        {
            item: ENCOUNTER_TYPES.BATTLE,
            weight: BATTLE_WEIGHT,
        },
        {
            item: ENCOUNTER_TYPES.BUFF,
            weight: BUFF_WEIGHT,
        },
        {
            item: ENCOUNTER_TYPES.CHARACTER,
            weight: CHARACTER_WEIGHT,
        },
    ];

    if (hasScavengeNodes) {
        items.push({
            item: ENCOUNTER_TYPES.SCAVENGE,
            weight: SCAVENGE_NODE_WEIGHT,
        });
    }

    let i: number;
    const weights: number[] = [];

    for (i = 0; i < items.length; i++) {
        weights[i] = items[i].weight + (weights[i - 1] || 0);
    }

    const random = Math.random() * weights.at(-1)!;

    for (i = 0; i < weights.length; i++) {
        if (weights[i] > random) {
            break;
        }
    }

    return items[i].item;
}

export const GenerateMap = ({
    hasScavengeNodes,
    buffs,
    location,
}: {
    hasScavengeNodes: boolean;
    buffs: MapBuffs;
    location: LocationTypes;
}) => {
    // Map is generated as a list of nodes containing edges and encounter types
    // Specific details (such as type of enemy) for each node are generated at encounter time
    const nodesToGenerate: number = Math.random() * (MAX_NODES - MIN_NODES) + MIN_NODES;
    const nodes: MapNodeType[] = [];

    // First, populate all the nodes with encounter types
    nodes.push({ encounterType: ENCOUNTER_TYPES.BASE });

    for (let i = 1; i < nodesToGenerate - 1; ++i) {
        nodes.push({ encounterType: GetWeightedRandomNodeType(hasScavengeNodes), edges: [] });
    }

    nodes.push({ encounterType: ENCOUNTER_TYPES.BOSS });

    // Now, generate edges for the nodes

    // Free nodes are removed from the list whenever an edge is assigned to it
    // The boss and base nodes are special, and are not added to this list
    const freeNodes: number[] = [];
    for (let i = 1; i < nodes.length - 1; ++i) {
        freeNodes.push(i);
    }

    const nodeQueue: number[] = [];

    function SelectEdge(): number {
        const selected: number = freeNodes[Math.floor(Math.random() * freeNodes.length)];
        nodeQueue.push(selected);
        freeNodes.splice(freeNodes.indexOf(selected), 1);
        return selected;
    }

    // Base node always requires 2 edges
    nodes[0].edges = [SelectEdge(), SelectEdge(), SelectEdge()];

    // Intermediate nodes can have 1 or 2 edges.

    // TODO:
    // May need to balance the tree so we don't have too many nodes on one side
    // To do this we can keep track of the number of 2-edge nodes on each branch from the base;
    // the more 2-edge nodes the less likely another one should be generated
    let doubleEdgedNodes = 0;
    while (freeNodes.length > 0) {
        const currentNodeIndex = nodeQueue.shift()!;
        const currentNode = nodes[currentNodeIndex];
        if (!currentNode) {
            throw new Error(`GenerateMap: Expected node at index ${currentNodeIndex} but found undefined.`);
        }

        // Ensure edges array exists
        if (!currentNode.edges) {
            currentNode.edges = [];
        }

        const numberOfEdges: number =
            doubleEdgedNodes >= MAX_DOUBLE_EDGED_NODES || Math.random() > DOUBLE_EDGE_CHANCE ? 1 : 2;
        if (numberOfEdges === 2) {
            ++doubleEdgedNodes;
        }

        for (let i = 0; i < numberOfEdges; ++i) {
            if (freeNodes.length > 0) {
                currentNode.edges.push(SelectEdge());
            }
        }
    }

    // ran out of free nodes. Add edges to all dangling nodes
    while (nodeQueue.length > 0) {
        const currentNodeIndex = nodeQueue.shift()!;
        const currentNode = nodes[currentNodeIndex];
        if (!currentNode) {
            throw new Error(`GenerateMap: Expected node at index ${currentNodeIndex} but found undefined.`);
        }
        if (!currentNode.edges) {
            currentNode.edges = [];
        }
        currentNode.edges.push(nodes.length - 1);
    }

    // join some paths up by finding nodes of d and d + 1 where d + 1 only has one edge and is adjacent to d
    const { strBuff, defBuff, dexBuff } = buffs;

    return {
        playerLocation: 0,
        currentNodeComplete: true,
        mapComplete: false,
        strBuff,
        defBuff,
        dexBuff,
        nodes: nodes,
        location,
    };
};

export const GetCharacterDialogue = (goodOutcome: boolean, mapLevel: number) => {
    const selectedCharacter: string = CHARACTER_NAMES[Math.floor(Math.random() * CHARACTER_NAMES.length)];
    const selectedLocation: string =
        ENCOUNTER_LOCATIONS_IMAGES[Math.floor(Math.random() * ENCOUNTER_LOCATIONS_IMAGES.length)];
    const selectedLine: string = goodOutcome
        ? GOOD_DIALOGUE_LINES[Math.floor(Math.random() * GOOD_DIALOGUE_LINES.length)]
        : BAD_DIALOGUE_LINES[Math.floor(Math.random() * BAD_DIALOGUE_LINES.length)];
    const cashReward: number = randomIntFromInterval(20 + mapLevel * 10, 20 + mapLevel * 30);

    // 5% chance of encountering Apollo
    if (goodOutcome && Math.random() <= 0.05) {
        const apolloDialogue: string = APOLLO_DIALOGUE[Math.floor(Math.random() * APOLLO_DIALOGUE.length)];
        return {
            character: "Apollo",
            location: selectedLocation,
            line: apolloDialogue,
            isItemDrop: false,
            rewards: 0,
            mugged: false,
        };
    }

    // 40% chance of item drop
    if (goodOutcome && Math.random() <= 0.4) {
        return {
            character: selectedCharacter,
            location: selectedLocation,
            line: selectedLine,
            isItemDrop: true,
            rewards: cashReward,
            mugged: !goodOutcome,
        };
    }

    return {
        character: selectedCharacter,
        location: selectedLocation,
        line: selectedLine,
        isItemDrop: false,
        rewards: cashReward,
        mugged: !goodOutcome,
    };
};

export const GetDropId = async (user: UserModel) => {
    const map = user.roguelikeMap;

    if (!map) {
        return 0;
    }

    const dropChanceType = map.mapComplete ? DropChanceTypes.boss : DropChanceTypes.roguelike;

    const potentialDrops = await findPotentialDropsForRoguelike(user.roguelikeLevel, map.location);

    const applicableQuests = await findApplicableQuestsForDrops(user.id, map.location);

    // insert quest drop rolls first, unless it's a boss drop table
    for (const applicableQuest of applicableQuests) {
        // Check if the quest has objectives
        if (!applicableQuest.quest?.quest_objective || applicableQuest.quest.quest_objective.length === 0) {
            continue;
        }

        // Get first ACQUIRE_ITEM objective for the quest at this location
        const questObjective = applicableQuest.quest.quest_objective[0];

        if (questObjective.target === null || questObjective.quantity === null) {
            continue;
        }

        const hasAllQuestItems: boolean = await InventoryService.UserHasNumberOfItem(
            user.id,
            questObjective.target,
            questObjective.quantity
        );

        if (hasAllQuestItems) {
            // Dont add quest drops if user has all quest items
            break;
        }

        let drop = await findDropChanceByItemId(questObjective.target);

        // Default quest items to 20% droprate if no dropChance
        if (!drop || !drop.dropRate) {
            drop = {
                itemId: questObjective.target,
                dropRate: 1.1, // temporarily increase due to global droprate nerf
            } as typeof drop;
        }

        // Only add if drop is not null
        if (drop) {
            if (dropChanceType === DropChanceTypes.boss) {
                potentialDrops.push(drop);
            } else {
                potentialDrops.unshift(drop);
            }
        }
    }
    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("rareDrops", user.id);
    for (const drop of potentialDrops) {
        if (shrineBuffActive && drop.dropRate < 0.08) {
            drop.dropRate *= shrineBuffActive;
        }
        let dropRate: number = drop.dropRate / 4.5; // TEMPORARY
        if (dropRate < 0.05) {
            dropRate = dropRate * 0.5;
        }
        if (Math.random() <= dropRate) {
            return drop.itemId;
        }
    }

    return 0;
};

export const GetScavengeItemDrop = async (user: UserModel, map: MapType, choice: string) => {
    const potentialDrops = await findScavengeDrops(user.roguelikeLevel, map.location, choice);

    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }

    // Pick a random item from the potential drops
    const randomIndex: number = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];

    // Check if itemId and item are not null
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }

    // Add the item to the user's inventory
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: selectedItem.quantity,
        isTradeable: true,
    });

    // Emit item dropped event for scavenge
    await emitItemDropped({
        userId: user.id,
        itemId: selectedItem.itemId,
        quantity: selectedItem.quantity,
        source: "scavenge",
    });

    logAction({
        action: "SCAVENGE_ITEM",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: selectedItem.quantity,
        },
    });

    return {
        itemReward: selectedItem.item,
        itemQuantity: selectedItem.quantity,
    };
};

export const GetScavengeBadOutcome = async (currentUser: UserModel, map: MapType, choice: string) => {
    const jailChance = 0.2; // 20% chance to be jailed / 80% chance to get an injury
    const outcome: { jailed?: boolean; jailDuration?: number; injury?: StatusEffectModel } = {};

    if (Math.random() <= jailChance) {
        // Send user to jail
        const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail", currentUser.id)) || 1;
        const jailDuration: number = DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive;

        await UserService.JailUser(currentUser.id, jailDuration, "Caught scavenging", {
            notificationType: "jail",
        });

        outcome.jailed = true;
        outcome.jailDuration = jailDuration;

        logAction({
            action: "SCAVENGE_JAILED",
            userId: currentUser.id,
            info: {
                jailDuration: jailDuration,
            },
        });

        await NotificationService.NotifyUser(
            currentUser.id,
            NotificationTypes.jail,
            {
                reason: "scavenging",
                duration: jailDuration,
            },
            true
        );
    } else {
        const scavengeData: ScavengeLocation = getScavengeLocation(map.currentNodeChoices || []);

        // Make sure choice exists in choices
        if (!scavengeData.choices[choice]) {
            throw new Error(`Invalid scavenge choice: ${choice}`);
        }

        const injuryType: InjuryType = scavengeData.choices[choice].injury;

        // Apply injury to user
        const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
        if (!injury) {
            throw new Error(`No minor injury found for type: ${injuryType}`);
        }
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);

        outcome.injury = injury;

        await NotificationService.NotifyUser(
            currentUser.id,
            NotificationTypes.injured,
            {
                reason: "scavenging",
                injury: injury.name,
                injuryTier: injury.tier,
            },
            true
        );

        logAction({
            action: "SCAVENGE_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
            },
        });
    }

    return outcome;
};
