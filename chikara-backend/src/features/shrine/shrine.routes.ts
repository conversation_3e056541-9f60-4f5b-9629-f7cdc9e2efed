import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { getToday } from "../../utils/dateHelpers.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as ShrineController from "./shrine.controller.js";
import * as ShrineHelper from "./shrine.helpers.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";
import { getDonateToShrineSchema } from "./shrine.validation.js";

export const shrineRouter = {
    getGoal: isLoggedInAuth.handler(async () => {
        const today = getToday();
        return await ShrineHelper.getDailyShrineGoal(today);
    }),

    getDonations: isLoggedInAuth.handler(async () => {
        const today = getToday();
        return await ShrineRepository.getDailyDonations(today);
    }),

    getActiveBuff: isLoggedInAuth.handler(async ({ context }) => {
        return await ShrineController.getActiveBuffsForUser(context.user.id);
    }),

    getUserDonationStatus: isLoggedInAuth.handler(async ({ context }) => {
        return await ShrineController.getUserDonationStatus(context.user.id);
    }),

    donate: canMakeStateChangesAuth.input(getDonateToShrineSchema()).handler(async ({ input, context }) => {
        const response = await ShrineController.donateToShrine(context.user.id, input.amount);
        return handleResponse(response);
    }),
};
