import * as NotificationService from "../../../../core/notification.service.js";
import * as TalentHelper from "../../../../features/talents/talents.helpers.js";
import * as ShrineHelper from "../../../../features/shrine/shrine.helpers.js";
import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { NotificationTypes } from "../../../../types/notification.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
import * as mathjs from "mathjs";

interface InvestorTalent {
    modifier: number;
}

async function processJobPayments(hour: number): Promise<void> {
    logger.profile("processJobPayments");
    try {
        const payees = await db.user.findMany({
            where: {
                jobId: { not: null },
                jobPayoutHour: hour,
            },
        });

        if (!payees || payees.length === 0) return;

        logger.info(`Running hour ${hour} job payouts for ${payees.length} users`);

        const jobs = await db.job.findMany();
        const formulaMap = new Map<number, string>();
        for (const job of jobs) {
            formulaMap.set(job.id, job.payFormula || "");
        }

        const calculationResults = new Map<string, number>();

        for (const payee of payees) {
            if (payee.blockNextJobPayout) {
                await db.user.update({
                    where: { id: payee.id },
                    data: { blockNextJobPayout: false },
                });
                continue;
            }

            const paymentFormula = formulaMap.get(payee.jobId!);
            if (!paymentFormula) {
                logger.error(`Could not find payment formula for job ${payee.jobId}`);
                continue;
            }
            const calculationKey = paymentFormula + payee.jobLevel;

            let result = calculationResults.get(calculationKey);
            if (!result) {
                result = mathjs.evaluate(paymentFormula, { n: payee.jobLevel });
                if (!result) {
                    logger.error(`Failed to calculate payment for job ${payee.jobId} with formula ${paymentFormula}`);
                    continue;
                }
                calculationResults.set(calculationKey, result);
            }

            const investor = (await TalentHelper.UserHasInvestorTalent(payee.id)) as InvestorTalent | null;
            if (investor) {
                result *= investor.modifier;
            }
            
            // Apply shrine yenEarnings buff if active
            const yenEarningsBuff = await ShrineHelper.dailyBuffIsActive("yenEarnings", payee.id);
            if (yenEarningsBuff) {
                result *= yenEarningsBuff;
            }
            
            const roundedResult = Math.round(result);

            await db.user.update({
                where: { id: payee.id },
                data: { cash: { increment: roundedResult } },
            });

            logAction({
                action: "JOB_PAYMENT",
                userId: payee.id,
                info: {
                    amount: roundedResult,
                    jobId: payee.jobId,
                },
            });

            NotificationService.NotifyUser(payee.id, NotificationTypes.job_paid, {
                amount: roundedResult,
                job: payee.jobId,
            });
        }
    } catch (error) {
        LogErrorStack({ message: "Failed to process job payments:", error });
    }
    logger.profile("processJobPayments");
}

export default processJobPayments;
